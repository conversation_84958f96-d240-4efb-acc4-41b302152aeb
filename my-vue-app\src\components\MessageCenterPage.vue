<template>
  <div class="message-center-page">
    <!-- 顶部容器 -->
    <div class="top-container">
      <!-- 左侧图标 -->
      <div class="left-icon-container">
        <img :src="messageIcon1" alt="消息图标1" class="message-icon-1" />
      </div>
      
      <!-- 右侧图标 -->
      <div class="right-icon-container">
        <img :src="messageIcon2" alt="消息图标2" class="message-icon-2" />
      </div>
    </div>

    <!-- 返回按钮 -->
    <div class="back-button" @click="goBack">
      <img :src="arrowLeft" alt="返回" class="arrow-left" />
    </div>

    <!-- 分隔条 -->
    <div class="separator"></div>

    <!-- 页面标题 -->
    <div class="page-title">消息中心</div>

    <!-- 消息列表 -->
    <div class="message-list">
      <!-- 第一条消息 -->
      <div class="message-item">
        <div class="message-content">
          <div class="message-title">新任务</div>
          <div class="message-description">老师布置了作文任务，快去查看吧！～</div>
          <div class="message-time">12:44 PM</div>
          <div class="message-badge">
            <div class="badge-circle">
              <span class="badge-text">1</span>
            </div>
          </div>
        </div>
        <div class="message-divider"></div>
      </div>

      <!-- 第二条消息 -->
      <div class="message-item">
        <div class="message-content">
          <div class="message-title">新的作文报告</div>
          <div class="message-description">本学年第三单元的作文报告已生成，快去查看吧！～</div>
          <div class="message-time">12:44 PM</div>
          <div class="message-badge">
            <div class="badge-circle">
              <span class="badge-text">1</span>
            </div>
          </div>
        </div>
        <div class="message-divider"></div>
      </div>

      <!-- 第三条消息 -->
      <div class="message-item">
        <div class="message-content">
          <div class="message-title">新任务</div>
          <div class="message-description">老师布置了作文任务，快去查看吧！～</div>
          <div class="message-time">04.02  星期三</div>
        </div>
        <div class="message-divider"></div>
      </div>

      <!-- 第四条消息 -->
      <div class="message-item">
        <div class="message-content">
          <div class="message-title">新任务</div>
          <div class="message-description">老师布置了作文任务，快去查看吧！～</div>
          <div class="message-time">2024.12.22  星期一</div>
        </div>
        <div class="message-divider"></div>
      </div>
    </div>
  </div>
</template>

<script>
import messageIcon1 from '../assets/images/message-icon-1.svg'
import messageIcon2 from '../assets/images/message-icon-2.svg'
import arrowLeft from '../assets/images/arrow-left.svg'

export default {
  name: 'MessageCenterPage',
  emits: ['go-back'],
  data() {
    return {
      messageIcon1,
      messageIcon2,
      arrowLeft
    }
  },
  methods: {
    goBack() {
      this.$emit('go-back')
    }
  }
}
</script>

<style scoped>
.message-center-page {
  width: 390px;
  height: 844px;
  background: #FFFFFF;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  position: relative;
  margin: 0 auto;
}

.top-container {
  width: 390px;
  height: 40px;
  border-bottom: 1px solid #F3F4F6;
  position: relative;
}

.left-icon-container {
  width: 72px;
  height: 40px;
  border-right: 1px solid #BCC1CA;
  position: absolute;
  left: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-icon-1 {
  width: 27.34px;
  height: 10.7px;
}

.right-icon-container {
  width: 96px;
  height: 40px;
  border-left: 1px solid #BCC1CA;
  position: absolute;
  right: 0;
  top: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.message-icon-2 {
  width: 65.87px;
  height: 10.56px;
}

.back-button {
  width: 24px;
  height: 24px;
  position: absolute;
  left: 24px;
  top: 48px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.arrow-left {
  width: 17.2px;
  height: 12.04px;
}

.separator {
  width: 375px;
  height: 4px;
  background: #F8F9FA;
  position: absolute;
  left: 0;
  top: 80px;
}

.page-title {
  position: absolute;
  left: 163px;
  top: 46px;
  width: 64px;
  height: 26px;
  font-family: Inter;
  font-weight: 400;
  font-size: 16px;
  line-height: 26px;
  color: #323842;
}

.message-list {
  position: absolute;
  left: 16px;
  top: 120px;
  width: 358px;
}

.message-item {
  width: 358px;
  margin-bottom: 13px;
}

.message-content {
  width: 358px;
  height: 56px;
  position: relative;
}

.message-title {
  position: absolute;
  left: 17px;
  top: 0;
  font-family: Inter;
  font-weight: 500;
  font-size: 14px;
  line-height: 22px;
  color: #171A1F;
}

.message-description {
  position: absolute;
  left: 17px;
  top: 24px;
  font-family: Inter;
  font-weight: 400;
  font-size: 12px;
  line-height: 20px;
  color: #171A1F;
  max-width: 248px;
}

.message-time {
  position: absolute;
  right: 2px;
  top: 2px;
  font-family: Inter;
  font-weight: 400;
  font-size: 11px;
  line-height: 18px;
  text-align: right;
  color: #424955;
}

.message-badge {
  position: absolute;
  right: 16px;
  top: 26px;
  width: 16px;
  height: 16px;
}

.badge-circle {
  width: 16px;
  height: 16px;
  background: #636AE8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.badge-text {
  font-family: Inter;
  font-weight: 400;
  font-size: 10px;
  line-height: 16px;
  text-align: center;
  color: #6E7787;
}

.message-divider {
  width: 358px;
  height: 1px;
  background: #F3F4F6;
  margin-top: 56px;
}

/* 第三和第四条消息的描述文字颜色 */
.message-item:nth-child(3) .message-description,
.message-item:nth-child(4) .message-description {
  color: #9095A0;
}

.message-item:nth-child(3) .message-time,
.message-item:nth-child(4) .message-time {
  color: #9095A0;
}
</style>
